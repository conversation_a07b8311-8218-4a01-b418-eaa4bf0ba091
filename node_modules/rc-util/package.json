{"name": "rc-util", "version": "5.44.4", "description": "Common Utils For React Component", "keywords": ["react", "util"], "homepage": "http://github.com/react-component/util", "bugs": {"url": "http://github.com/react-component/util/issues"}, "repository": {"type": "git", "url": "**************:react-component/util.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es"], "scripts": {"build": "dumi build", "compile": "father build", "coverage": "npm test -- --coverage", "lint": "eslint src/ --ext .tsx,.ts & eslint tests/ --ext .js", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.18.3", "react-is": "^18.2.0"}, "devDependencies": {"@rc-component/father-plugin": "1.0.0", "@testing-library/react": "^16.0.0", "@types/jest": "^29.4.0", "@types/node": "^22.5.5", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-is": "^18.3.0", "@types/responselike": "^1.0.0", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "create-react-class": "^15.6.3", "cross-env": "^7.0.2", "dumi": "^2.1.3", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-unicorn": "^55.0.0", "father": "^4.1.3", "glob": "^9.2.1", "husky": "^9.1.6", "lint-staged": "^15.1.0", "np": "^10.0.2", "prettier": "^3.3.2", "rc-test": "^7.0.14", "react": "^18.0.0", "react-19": "npm:react@19.0.0", "react-dom": "^18.0.0", "react-dom-19": "npm:react-dom@19.0.0", "typescript": "^5.3.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}