import * as React from 'react';
import type { GetIndicatorSize } from '../hooks/useIndicator';
import type { AnimatedConfig, EditableConfig, MoreProps, OnTabScroll, RenderTabBar, TabBarExtraContent, TabPosition, TabsLocale } from '../interface';
export interface TabNavListProps {
    id: string;
    tabPosition: TabPosition;
    activeKey: string;
    rtl: boolean;
    animated?: AnimatedConfig;
    extra?: TabBarExtraContent;
    editable?: EditableConfig;
    more?: MoreProps;
    mobile: boolean;
    tabBarGutter?: number;
    renderTabBar?: RenderTabBar;
    className?: string;
    style?: React.CSSProperties;
    locale?: TabsLocale;
    onTabClick: (activeKey: string, e: React.MouseEvent | React.KeyboardEvent) => void;
    onTabScroll?: OnTabScroll;
    children?: (node: React.ReactElement) => React.ReactElement;
    getPopupContainer?: (node: HTMLElement) => HTMLElement;
    popupClassName?: string;
    indicator?: {
        size?: GetIndicatorSize;
        align?: 'start' | 'center' | 'end';
    };
}
declare const TabNavList: React.ForwardRefExoticComponent<TabNavListProps & React.RefAttributes<HTMLDivElement>>;
export default TabNavList;
