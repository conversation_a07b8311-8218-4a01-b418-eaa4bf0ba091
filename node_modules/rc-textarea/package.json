{"name": "rc-textarea", "version": "1.10.2", "description": "Pretty Textarea react component used in used in ant.design", "keywords": ["react", "react-component", "react-textarea", "textarea", "antd", "ant-design"], "main": "./lib/index", "module": "./es/index", "files": ["assets/*.css", "assets/*.less", "es", "lib", "dist"], "homepage": "https://react-component.github.io/textarea", "repository": {"type": "git", "url": "**************:react-component/textarea.git"}, "bugs": {"url": "http://github.com/react-component/textarea/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build && lessc assets/index.less assets/index.css", "gh-pages": "npm run docs:build && npm run docs:deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch=antd-5.x", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "pretty-quick": "pretty-quick", "test": "umi-test", "coverage": "umi-test --coverage"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.1", "rc-input": "~1.8.0", "rc-resize-observer": "^1.0.0", "rc-util": "^5.27.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^15.0.0", "@types/classnames": "^2.2.9", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.0.8", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "dumi": "^2.0.0", "eslint": "^7.0.0", "father": "^4.0.0", "gh-pages": "^3.1.0", "husky": "^4.2.5", "less": "^3.10.3", "np": "^10.2.0", "prettier": "^2.0.5", "pretty-quick": "^2.0.1", "react": "^18.0.0", "react-dom": "^18.0.0", "cheerio": "1.0.0-rc.12", "umi-test": "^1.9.7"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}}