"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classnames = _interopRequireDefault(require("classnames"));
var _rcInput = require("rc-input");
var _useCount = _interopRequireDefault(require("rc-input/lib/hooks/useCount"));
var _commonUtils = require("rc-input/lib/utils/commonUtils");
var _useMergedState3 = _interopRequireDefault(require("rc-util/lib/hooks/useMergedState"));
var _react = _interopRequireWildcard(require("react"));
var _ResizableTextArea = _interopRequireDefault(require("./ResizableTextArea"));
var _excluded = ["defaultValue", "value", "onFocus", "onBlur", "onChange", "allowClear", "maxLength", "onCompositionStart", "onCompositionEnd", "suffix", "prefixCls", "showCount", "count", "className", "style", "disabled", "hidden", "classNames", "styles", "onResize", "onClear", "onPressEnter", "readOnly", "autoSize", "onKeyDown"];
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var TextArea = /*#__PURE__*/_react.default.forwardRef(function (_ref, ref) {
  var _countConfig$max;
  var defaultValue = _ref.defaultValue,
    customValue = _ref.value,
    onFocus = _ref.onFocus,
    onBlur = _ref.onBlur,
    onChange = _ref.onChange,
    allowClear = _ref.allowClear,
    maxLength = _ref.maxLength,
    onCompositionStart = _ref.onCompositionStart,
    onCompositionEnd = _ref.onCompositionEnd,
    suffix = _ref.suffix,
    _ref$prefixCls = _ref.prefixCls,
    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,
    showCount = _ref.showCount,
    count = _ref.count,
    className = _ref.className,
    style = _ref.style,
    disabled = _ref.disabled,
    hidden = _ref.hidden,
    classNames = _ref.classNames,
    styles = _ref.styles,
    onResize = _ref.onResize,
    onClear = _ref.onClear,
    onPressEnter = _ref.onPressEnter,
    readOnly = _ref.readOnly,
    autoSize = _ref.autoSize,
    onKeyDown = _ref.onKeyDown,
    rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);
  var _useMergedState = (0, _useMergedState3.default)(defaultValue, {
      value: customValue,
      defaultValue: defaultValue
    }),
    _useMergedState2 = (0, _slicedToArray2.default)(_useMergedState, 2),
    value = _useMergedState2[0],
    setValue = _useMergedState2[1];
  var formatValue = value === undefined || value === null ? '' : String(value);
  var _React$useState = _react.default.useState(false),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    focused = _React$useState2[0],
    setFocused = _React$useState2[1];
  var compositionRef = _react.default.useRef(false);
  var _React$useState3 = _react.default.useState(null),
    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
    textareaResized = _React$useState4[0],
    setTextareaResized = _React$useState4[1];

  // =============================== Ref ================================
  var holderRef = (0, _react.useRef)(null);
  var resizableTextAreaRef = (0, _react.useRef)(null);
  var getTextArea = function getTextArea() {
    var _resizableTextAreaRef;
    return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;
  };
  var focus = function focus() {
    getTextArea().focus();
  };
  (0, _react.useImperativeHandle)(ref, function () {
    var _holderRef$current;
    return {
      resizableTextArea: resizableTextAreaRef.current,
      focus: focus,
      blur: function blur() {
        getTextArea().blur();
      },
      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || getTextArea()
    };
  });
  (0, _react.useEffect)(function () {
    setFocused(function (prev) {
      return !disabled && prev;
    });
  }, [disabled]);

  // =========================== Select Range ===========================
  var _React$useState5 = _react.default.useState(null),
    _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),
    selection = _React$useState6[0],
    setSelection = _React$useState6[1];
  _react.default.useEffect(function () {
    if (selection) {
      var _getTextArea;
      (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0, _toConsumableArray2.default)(selection));
    }
  }, [selection]);

  // ============================== Count ===============================
  var countConfig = (0, _useCount.default)(count, showCount);
  var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;

  // Max length value
  var hasMaxLength = Number(mergedMax) > 0;
  var valueLength = countConfig.strategy(formatValue);
  var isOutOfRange = !!mergedMax && valueLength > mergedMax;

  // ============================== Change ==============================
  var triggerChange = function triggerChange(e, currentValue) {
    var cutValue = currentValue;
    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {
      cutValue = countConfig.exceedFormatter(currentValue, {
        max: countConfig.max
      });
      if (currentValue !== cutValue) {
        setSelection([getTextArea().selectionStart || 0, getTextArea().selectionEnd || 0]);
      }
    }
    setValue(cutValue);
    (0, _commonUtils.resolveOnChange)(e.currentTarget, e, onChange, cutValue);
  };

  // =========================== Value Update ===========================
  var onInternalCompositionStart = function onInternalCompositionStart(e) {
    compositionRef.current = true;
    onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);
  };
  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {
    compositionRef.current = false;
    triggerChange(e, e.currentTarget.value);
    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);
  };
  var onInternalChange = function onInternalChange(e) {
    triggerChange(e, e.target.value);
  };
  var handleKeyDown = function handleKeyDown(e) {
    if (e.key === 'Enter' && onPressEnter) {
      onPressEnter(e);
    }
    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);
  };
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    onFocus === null || onFocus === void 0 || onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setFocused(false);
    onBlur === null || onBlur === void 0 || onBlur(e);
  };

  // ============================== Reset ===============================
  var handleReset = function handleReset(e) {
    setValue('');
    focus();
    (0, _commonUtils.resolveOnChange)(getTextArea(), e, onChange);
  };
  var suffixNode = suffix;
  var dataCount;
  if (countConfig.show) {
    if (countConfig.showFormatter) {
      dataCount = countConfig.showFormatter({
        value: formatValue,
        count: valueLength,
        maxLength: mergedMax
      });
    } else {
      dataCount = "".concat(valueLength).concat(hasMaxLength ? " / ".concat(mergedMax) : '');
    }
    suffixNode = /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, suffixNode, /*#__PURE__*/_react.default.createElement("span", {
      className: (0, _classnames.default)("".concat(prefixCls, "-data-count"), classNames === null || classNames === void 0 ? void 0 : classNames.count),
      style: styles === null || styles === void 0 ? void 0 : styles.count
    }, dataCount));
  }
  var handleResize = function handleResize(size) {
    var _getTextArea2;
    onResize === null || onResize === void 0 || onResize(size);
    if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {
      setTextareaResized(true);
    }
  };
  var isPureTextArea = !autoSize && !showCount && !allowClear;
  return /*#__PURE__*/_react.default.createElement(_rcInput.BaseInput, {
    ref: holderRef,
    value: formatValue,
    allowClear: allowClear,
    handleReset: handleReset,
    suffix: suffixNode,
    prefixCls: prefixCls,
    classNames: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, classNames), {}, {
      affixWrapper: (0, _classnames.default)(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, "".concat(prefixCls, "-show-count"), showCount), "".concat(prefixCls, "-textarea-allow-clear"), allowClear))
    }),
    disabled: disabled,
    focused: focused,
    className: (0, _classnames.default)(className, isOutOfRange && "".concat(prefixCls, "-out-of-range")),
    style: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, style), textareaResized && !isPureTextArea ? {
      height: 'auto'
    } : {}),
    dataAttrs: {
      affixWrapper: {
        'data-count': typeof dataCount === 'string' ? dataCount : undefined
      }
    },
    hidden: hidden,
    readOnly: readOnly,
    onClear: onClear
  }, /*#__PURE__*/_react.default.createElement(_ResizableTextArea.default, (0, _extends2.default)({}, rest, {
    autoSize: autoSize,
    maxLength: maxLength,
    onKeyDown: handleKeyDown,
    onChange: onInternalChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onCompositionStart: onInternalCompositionStart,
    onCompositionEnd: onInternalCompositionEnd,
    className: (0, _classnames.default)(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),
    style: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {
      resize: style === null || style === void 0 ? void 0 : style.resize
    }),
    disabled: disabled,
    prefixCls: prefixCls,
    onResize: handleResize,
    ref: resizableTextAreaRef,
    readOnly: readOnly
  })));
});
var _default = exports.default = TextArea;