{"name": "rc-tree-select", "version": "5.27.0", "description": "tree-select ui component for react", "keywords": ["react", "react-component", "react-tree-select", "tree-select"], "homepage": "https://github.com/react-component/tree-select", "bugs": {"url": "https://github.com/react-component/tree-select/issues"}, "repository": {"type": "git", "url": "https://github.com/react-component/tree-select.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "files": ["es", "lib", "dist", "assets/*.less", "assets/*.css", "assets/*.png", "assets/*.gif"], "scripts": {"build": "dumi build", "compile": "father build", "lint": "eslint src/ examples/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepare": "husky && dumi setup", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "prettier": "prettier '{src,tests}/**/*.{ts,tsx}' 'tests/**/*.js' --write", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}, "dependencies": {"@babel/runtime": "^7.25.7", "classnames": "2.x", "rc-select": "~14.16.2", "rc-tree": "~5.13.0", "rc-util": "^5.43.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.1.0", "@rc-component/trigger": "^1.18.3", "@testing-library/react": "^12.1.5", "@types/jest": "^29.5.13", "@types/node": "^22.7.5", "@types/react": "^18.3.11", "@types/react-dom": "^19.0.1", "@types/warning": "^3.0.3", "@umijs/fabric": "^4.0.1", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.3", "dumi": "^2.4.12", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.8", "enzyme-to-json": "^3.6.2", "eslint": "^8.57.1", "eslint-plugin-jest": "^28.10.0", "eslint-plugin-unicorn": "^56.0.0", "father": "^4.5.0", "glob": "^11.0.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "np": "^10.0.7", "prettier": "^3.3.3", "rc-dialog": "^9.6.0", "rc-field-form": "^2.4.0", "rc-test": "^7.1.1", "rc-virtual-list": "^3.14.8", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^5.6.3"}, "peerDependencies": {"react": "*", "react-dom": "*"}}