{"name": "rc-steps", "version": "6.0.1", "description": "steps ui component for react", "keywords": ["react", "react-component", "react-steps"], "homepage": "http://github.com/react-component/steps", "bugs": {"url": "http://github.com/react-component/steps/issues"}, "repository": {"type": "git", "url": " git+ssh://**************/react-component/steps.git"}, "license": "MIT", "maintainers": [{"name": "afc163", "email": "<EMAIL>"}], "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "files": ["assets/*.css", "dist", "es", "lib"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "umi-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "gh-pages": "npm run docs:build && npm run docs:deploy", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "postpublish": "npm run gh-pages", "start": "dumi dev", "test": "umi-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write"]}, "dependencies": {"@babel/runtime": "^7.16.7", "classnames": "^2.2.3", "rc-util": "^5.16.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.11", "@types/jest": "^26.0.5", "@types/react": "^16.9.2", "@types/react-dom": "^16.9.0", "@umijs/fabric": "^2.10.0", "cross-env": "^7.0.0", "dumi": "^1.1.38", "enzyme": "^3.1.0", "enzyme-adapter-react-16": "^1.0.1", "enzyme-to-json": "^3.1.2", "eslint": "^7.1.0", "father": "^4", "gh-pages": "^4.0.0", "husky": "^8.0.1", "less": "^3.11.2", "lint-staged": "^13.0.3", "np": "^7.6.0", "prettier": "^2.5.1", "querystring": "^0.2.0", "rc-dialog": "8.x", "rc-tools": "^9.6.1", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^4.5.4", "umi-test": "^1.9.7"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "engines": {"node": ">=8.x"}}