{"name": "rc-upload", "version": "4.9.2", "description": "upload ui component for react", "keywords": ["react", "react-component", "react-upload", "upload"], "homepage": "http://github.com/react-component/upload", "bugs": {"url": "http://github.com/react-component/upload/issues"}, "repository": {"type": "git", "url": "**************:react-component/upload.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es"], "scripts": {"compile": "father build", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "npm run docs:build && gh-pages -d dist", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "now-build": "npm run docs:build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "postpublish": "npm run docs:deploy", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.18.3", "classnames": "^2.2.5", "rc-util": "^5.2.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.11", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^4.0.1", "axios": "^1.9.0", "co-busboy": "^1.3.0", "coveralls": "^3.0.3", "cross-env": "^7.0.0", "dumi": "^2.1.0", "eslint": "^8.0.0", "father": "^4.0.0", "fs-extra": "^11.2.0", "gh-pages": "^6.1.1", "np": "^10.0.7", "raf": "^3.4.0", "rc-test": "^7.0.13", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.14.1", "sinon": "^9.0.2", "typescript": "^5.3.3", "vinyl-fs": "^4.0.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}