{"name": "rc-tooltip", "version": "6.4.0", "description": "React <PERSON>", "keywords": ["react", "react-component", "react-tooltip", "tooltip"], "homepage": "http://github.com/react-component/tooltip", "bugs": {"url": "http://github.com/react-component/tooltip/issues"}, "repository": {"type": "git", "url": "**************:react-component/tooltip.git"}, "license": "MIT", "maintainers": ["<EMAIL>"], "main": "lib/index", "module": "es/index", "files": ["lib", "es", "assets/*.css", "assets/*.less"], "scripts": {"compile": "father build && lessc assets/bootstrap.less assets/bootstrap.css && lessc assets/bootstrap_white.less assets/bootstrap_white.css", "docs:build": "dumi build", "docs:deploy": "gh-pages -d dist", "lint": "eslint src/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run docs:build", "prepare": "dumi setup", "prepublishOnly": "npm run compile ", "postpublish": "npm run docs:build && npm run docs:deploy", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.1", "rc-util": "^5.44.3"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.4.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@types/warning": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^2.2.13", "eslint": "^8.56.0", "eslint-plugin-unicorn": "^55.0.0", "father": "^4.0.0", "gh-pages": "^3.1.0", "less": "^4.1.1", "np": "^7.1.0", "rc-test": "^7.0.9", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^4.0.5"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}