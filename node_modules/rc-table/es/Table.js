import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _extends from "@babel/runtime/helpers/esm/extends";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
/**
 * Feature:
 *  - fixed not need to set width
 *  - support `rowExpandable` to config row expand logic
 *  - add `summary` to support `() => ReactNode`
 *
 * Update:
 *  - `dataIndex` is `array[]` now
 *  - `expandable` wrap all the expand related props
 *
 * Removed:
 *  - expandIconAsCell
 *  - useFixedHeader
 *  - rowRef
 *  - columns[number].onCellClick
 *  - onRowClick
 *  - onRowDoubleClick
 *  - onRowMouseEnter
 *  - onRowMouseLeave
 *  - getBodyWrapper
 *  - bodyStyle
 *
 * Deprecated:
 *  - All expanded props, move into expandable
 */

import classNames from 'classnames';
import ResizeObserver from 'rc-resize-observer';
import { isStyleSupport } from "rc-util/es/Dom/styleChecker";
import { getTargetScrollBarSize } from "rc-util/es/getScrollBarSize";
import useEvent from "rc-util/es/hooks/useEvent";
import pickAttrs from "rc-util/es/pickAttrs";
import getValue from "rc-util/es/utils/get";
import warning from "rc-util/es/warning";
import * as React from 'react';
import Body from "./Body";
import ColGroup from "./ColGroup";
import { EXPAND_COLUMN, INTERNAL_HOOKS } from "./constant";
import TableContext, { makeImmutable } from "./context/TableContext";
import FixedHolder from "./FixedHolder";
import Footer, { FooterComponents } from "./Footer";
import Summary from "./Footer/Summary";
import Header from "./Header/Header";
import useColumns from "./hooks/useColumns";
import useExpand from "./hooks/useExpand";
import useFixedInfo from "./hooks/useFixedInfo";
import { useTimeoutLock } from "./hooks/useFrame";
import useHover from "./hooks/useHover";
import useSticky from "./hooks/useSticky";
import useStickyOffsets from "./hooks/useStickyOffsets";
import Panel from "./Panel";
import StickyScrollBar from "./stickyScrollBar";
import Column from "./sugar/Column";
import ColumnGroup from "./sugar/ColumnGroup";
import { getColumnsKey, validateValue, validNumberValue } from "./utils/valueUtil";
import { getDOM } from "rc-util/es/Dom/findDOMNode";
import useLayoutEffect from "rc-util/es/hooks/useLayoutEffect";
export var DEFAULT_PREFIX = 'rc-table';

// Used for conditions cache
var EMPTY_DATA = [];

// Used for customize scroll
var EMPTY_SCROLL_TARGET = {};
function defaultEmpty() {
  return 'No Data';
}
function Table(tableProps, ref) {
  var props = _objectSpread({
    rowKey: 'key',
    prefixCls: DEFAULT_PREFIX,
    emptyText: defaultEmpty
  }, tableProps);
  var prefixCls = props.prefixCls,
    className = props.className,
    rowClassName = props.rowClassName,
    style = props.style,
    data = props.data,
    rowKey = props.rowKey,
    scroll = props.scroll,
    tableLayout = props.tableLayout,
    direction = props.direction,
    title = props.title,
    footer = props.footer,
    summary = props.summary,
    caption = props.caption,
    id = props.id,
    showHeader = props.showHeader,
    components = props.components,
    emptyText = props.emptyText,
    onRow = props.onRow,
    onHeaderRow = props.onHeaderRow,
    onScroll = props.onScroll,
    internalHooks = props.internalHooks,
    transformColumns = props.transformColumns,
    internalRefs = props.internalRefs,
    tailor = props.tailor,
    getContainerWidth = props.getContainerWidth,
    sticky = props.sticky,
    _props$rowHoverable = props.rowHoverable,
    rowHoverable = _props$rowHoverable === void 0 ? true : _props$rowHoverable;
  var mergedData = data || EMPTY_DATA;
  var hasData = !!mergedData.length;
  var useInternalHooks = internalHooks === INTERNAL_HOOKS;

  // ===================== Warning ======================
  if (process.env.NODE_ENV !== 'production') {
    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {
      warning(props[name] === undefined, "`".concat(name, "` is removed, please use `onRow` instead."));
    });
    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');
  }

  // ==================== Customize =====================
  var getComponent = React.useCallback(function (path, defaultComponent) {
    return getValue(components, path) || defaultComponent;
  }, [components]);
  var getRowKey = React.useMemo(function () {
    if (typeof rowKey === 'function') {
      return rowKey;
    }
    return function (record) {
      var key = record && record[rowKey];
      if (process.env.NODE_ENV !== 'production') {
        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');
      }
      return key;
    };
  }, [rowKey]);
  var customizeScrollBody = getComponent(['body']);

  // ====================== Hover =======================
  var _useHover = useHover(),
    _useHover2 = _slicedToArray(_useHover, 3),
    startRow = _useHover2[0],
    endRow = _useHover2[1],
    onHover = _useHover2[2];

  // ====================== Expand ======================
  var _useExpand = useExpand(props, mergedData, getRowKey),
    _useExpand2 = _slicedToArray(_useExpand, 6),
    expandableConfig = _useExpand2[0],
    expandableType = _useExpand2[1],
    mergedExpandedKeys = _useExpand2[2],
    mergedExpandIcon = _useExpand2[3],
    mergedChildrenColumnName = _useExpand2[4],
    onTriggerExpand = _useExpand2[5];

  // ====================== Column ======================
  var scrollX = scroll === null || scroll === void 0 ? void 0 : scroll.x;
  var _React$useState = React.useState(0),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    componentWidth = _React$useState2[0],
    setComponentWidth = _React$useState2[1];
  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {
      expandable: !!expandableConfig.expandedRowRender,
      columnTitle: expandableConfig.columnTitle,
      expandedKeys: mergedExpandedKeys,
      getRowKey: getRowKey,
      // https://github.com/ant-design/ant-design/issues/23894
      onTriggerExpand: onTriggerExpand,
      expandIcon: mergedExpandIcon,
      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,
      direction: direction,
      scrollWidth: useInternalHooks && tailor && typeof scrollX === 'number' ? scrollX : null,
      clientWidth: componentWidth
    }), useInternalHooks ? transformColumns : null),
    _useColumns2 = _slicedToArray(_useColumns, 4),
    columns = _useColumns2[0],
    flattenColumns = _useColumns2[1],
    flattenScrollX = _useColumns2[2],
    hasGapFixed = _useColumns2[3];
  var mergedScrollX = flattenScrollX !== null && flattenScrollX !== void 0 ? flattenScrollX : scrollX;
  var columnContext = React.useMemo(function () {
    return {
      columns: columns,
      flattenColumns: flattenColumns
    };
  }, [columns, flattenColumns]);

  // ======================= Refs =======================
  var fullTableRef = React.useRef();
  var scrollHeaderRef = React.useRef();
  var scrollBodyRef = React.useRef();
  var scrollBodyContainerRef = React.useRef();
  React.useImperativeHandle(ref, function () {
    return {
      nativeElement: fullTableRef.current,
      scrollTo: function scrollTo(config) {
        var _scrollBodyRef$curren3;
        if (scrollBodyRef.current instanceof HTMLElement) {
          // Native scroll
          var index = config.index,
            top = config.top,
            key = config.key;
          if (validNumberValue(top)) {
            var _scrollBodyRef$curren;
            (_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 || _scrollBodyRef$curren.scrollTo({
              top: top
            });
          } else {
            var _scrollBodyRef$curren2;
            var mergedKey = key !== null && key !== void 0 ? key : getRowKey(mergedData[index]);
            (_scrollBodyRef$curren2 = scrollBodyRef.current.querySelector("[data-row-key=\"".concat(mergedKey, "\"]"))) === null || _scrollBodyRef$curren2 === void 0 || _scrollBodyRef$curren2.scrollIntoView();
          }
        } else if ((_scrollBodyRef$curren3 = scrollBodyRef.current) !== null && _scrollBodyRef$curren3 !== void 0 && _scrollBodyRef$curren3.scrollTo) {
          // Pass to proxy
          scrollBodyRef.current.scrollTo(config);
        }
      }
    };
  });

  // ====================== Scroll ======================
  var scrollSummaryRef = React.useRef();
  var _React$useState3 = React.useState(false),
    _React$useState4 = _slicedToArray(_React$useState3, 2),
    pingedLeft = _React$useState4[0],
    setPingedLeft = _React$useState4[1];
  var _React$useState5 = React.useState(false),
    _React$useState6 = _slicedToArray(_React$useState5, 2),
    pingedRight = _React$useState6[0],
    setPingedRight = _React$useState6[1];
  var _React$useState7 = React.useState(new Map()),
    _React$useState8 = _slicedToArray(_React$useState7, 2),
    colsWidths = _React$useState8[0],
    updateColsWidths = _React$useState8[1];

  // Convert map to number width
  var colsKeys = getColumnsKey(flattenColumns);
  var pureColWidths = colsKeys.map(function (columnKey) {
    return colsWidths.get(columnKey);
  });
  var colWidths = React.useMemo(function () {
    return pureColWidths;
  }, [pureColWidths.join('_')]);
  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns, direction);
  var fixHeader = scroll && validateValue(scroll.y);
  var horizonScroll = scroll && validateValue(mergedScrollX) || Boolean(expandableConfig.fixed);
  var fixColumn = horizonScroll && flattenColumns.some(function (_ref) {
    var fixed = _ref.fixed;
    return fixed;
  });

  // Sticky
  var stickyRef = React.useRef();
  var _useSticky = useSticky(sticky, prefixCls),
    isSticky = _useSticky.isSticky,
    offsetHeader = _useSticky.offsetHeader,
    offsetSummary = _useSticky.offsetSummary,
    offsetScroll = _useSticky.offsetScroll,
    stickyClassName = _useSticky.stickyClassName,
    container = _useSticky.container;

  // Footer (Fix footer must fixed header)
  var summaryNode = React.useMemo(function () {
    return summary === null || summary === void 0 ? void 0 : summary(mergedData);
  }, [summary, mergedData]);
  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed;

  // Scroll
  var scrollXStyle;
  var scrollYStyle;
  var scrollTableStyle;
  if (fixHeader) {
    scrollYStyle = {
      overflowY: hasData ? 'scroll' : 'auto',
      maxHeight: scroll.y
    };
  }
  if (horizonScroll) {
    scrollXStyle = {
      overflowX: 'auto'
    };
    // When no vertical scrollbar, should hide it
    // https://github.com/ant-design/ant-design/pull/20705
    // https://github.com/ant-design/ant-design/issues/21879
    if (!fixHeader) {
      scrollYStyle = {
        overflowY: 'hidden'
      };
    }
    scrollTableStyle = {
      width: mergedScrollX === true ? 'auto' : mergedScrollX,
      minWidth: '100%'
    };
  }
  var onColumnResize = React.useCallback(function (columnKey, width) {
    updateColsWidths(function (widths) {
      if (widths.get(columnKey) !== width) {
        var newWidths = new Map(widths);
        newWidths.set(columnKey, width);
        return newWidths;
      }
      return widths;
    });
  }, []);
  var _useTimeoutLock = useTimeoutLock(null),
    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),
    setScrollTarget = _useTimeoutLock2[0],
    getScrollTarget = _useTimeoutLock2[1];
  function forceScroll(scrollLeft, target) {
    if (!target) {
      return;
    }
    if (typeof target === 'function') {
      target(scrollLeft);
    } else if (target.scrollLeft !== scrollLeft) {
      target.scrollLeft = scrollLeft;

      // Delay to force scroll position if not sync
      // ref: https://github.com/ant-design/ant-design/issues/37179
      if (target.scrollLeft !== scrollLeft) {
        setTimeout(function () {
          target.scrollLeft = scrollLeft;
        }, 0);
      }
    }
  }
  var onInternalScroll = useEvent(function (_ref2) {
    var currentTarget = _ref2.currentTarget,
      scrollLeft = _ref2.scrollLeft;
    var isRTL = direction === 'rtl';
    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;
    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;
    if (!getScrollTarget() || getScrollTarget() === compareTarget) {
      var _stickyRef$current;
      setScrollTarget(compareTarget);
      forceScroll(mergedScrollLeft, scrollHeaderRef.current);
      forceScroll(mergedScrollLeft, scrollBodyRef.current);
      forceScroll(mergedScrollLeft, scrollSummaryRef.current);
      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);
    }
    var measureTarget = currentTarget || scrollHeaderRef.current;
    if (measureTarget) {
      var scrollWidth =
      // Should use mergedScrollX in virtual table(useInternalHooks && tailor === true)
      useInternalHooks && tailor && typeof mergedScrollX === 'number' ? mergedScrollX : measureTarget.scrollWidth;
      var clientWidth = measureTarget.clientWidth;
      // There is no space to scroll
      if (scrollWidth === clientWidth) {
        setPingedLeft(false);
        setPingedRight(false);
        return;
      }
      if (isRTL) {
        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);
        setPingedRight(-mergedScrollLeft > 0);
      } else {
        setPingedLeft(mergedScrollLeft > 0);
        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);
      }
    }
  });
  var onBodyScroll = useEvent(function (e) {
    onInternalScroll(e);
    onScroll === null || onScroll === void 0 || onScroll(e);
  });
  var triggerOnScroll = function triggerOnScroll() {
    if (horizonScroll && scrollBodyRef.current) {
      var _scrollBodyRef$curren4;
      onInternalScroll({
        currentTarget: getDOM(scrollBodyRef.current),
        scrollLeft: (_scrollBodyRef$curren4 = scrollBodyRef.current) === null || _scrollBodyRef$curren4 === void 0 ? void 0 : _scrollBodyRef$curren4.scrollLeft
      });
    } else {
      setPingedLeft(false);
      setPingedRight(false);
    }
  };
  var onFullTableResize = function onFullTableResize(_ref3) {
    var _stickyRef$current2;
    var width = _ref3.width;
    (_stickyRef$current2 = stickyRef.current) === null || _stickyRef$current2 === void 0 || _stickyRef$current2.checkScrollBarVisible();
    var mergedWidth = fullTableRef.current ? fullTableRef.current.offsetWidth : width;
    if (useInternalHooks && getContainerWidth && fullTableRef.current) {
      mergedWidth = getContainerWidth(fullTableRef.current, mergedWidth) || mergedWidth;
    }
    if (mergedWidth !== componentWidth) {
      triggerOnScroll();
      setComponentWidth(mergedWidth);
    }
  };

  // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed
  var mounted = React.useRef(false);
  React.useEffect(function () {
    // onFullTableResize will be trigger once when ResizeObserver is mounted
    // This will reduce one duplicated triggerOnScroll time
    if (mounted.current) {
      triggerOnScroll();
    }
  }, [horizonScroll, data, columns.length]);
  React.useEffect(function () {
    mounted.current = true;
  }, []);

  // ===================== Effects ======================
  var _React$useState9 = React.useState(0),
    _React$useState10 = _slicedToArray(_React$useState9, 2),
    scrollbarSize = _React$useState10[0],
    setScrollbarSize = _React$useState10[1];
  var _React$useState11 = React.useState(true),
    _React$useState12 = _slicedToArray(_React$useState11, 2),
    supportSticky = _React$useState12[0],
    setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first

  useLayoutEffect(function () {
    if (!tailor || !useInternalHooks) {
      if (scrollBodyRef.current instanceof Element) {
        setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);
      } else {
        setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);
      }
    }
    setSupportSticky(isStyleSupport('position', 'sticky'));
  }, []);

  // ================== INTERNAL HOOKS ==================
  React.useEffect(function () {
    if (useInternalHooks && internalRefs) {
      internalRefs.body.current = scrollBodyRef.current;
    }
  });

  // ========================================================================
  // ==                               Render                               ==
  // ========================================================================
  // =================== Render: Func ===================
  var renderFixedHeaderTable = React.useCallback(function (fixedHolderPassProps) {
    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));
  }, [fixFooter, summaryNode]);
  var renderFixedFooterTable = React.useCallback(function (fixedHolderPassProps) {
    return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);
  }, [summaryNode]);

  // =================== Render: Node ===================
  var TableComponent = getComponent(['table'], 'table');

  // Table layout
  var mergedTableLayout = React.useMemo(function () {
    if (tableLayout) {
      return tableLayout;
    }
    // https://github.com/ant-design/ant-design/issues/25227
    // When scroll.x is max-content, no need to fix table layout
    // it's width should stretch out to fit content
    if (fixColumn) {
      return mergedScrollX === 'max-content' ? 'auto' : 'fixed';
    }
    if (fixHeader || isSticky || flattenColumns.some(function (_ref4) {
      var ellipsis = _ref4.ellipsis;
      return ellipsis;
    })) {
      return 'fixed';
    }
    return 'auto';
  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);
  var groupTableNode;

  // Header props
  var headerProps = {
    colWidths: colWidths,
    columCount: flattenColumns.length,
    stickyOffsets: stickyOffsets,
    onHeaderRow: onHeaderRow,
    fixHeader: fixHeader,
    scroll: scroll
  };

  // Empty
  var emptyNode = React.useMemo(function () {
    if (hasData) {
      return null;
    }
    if (typeof emptyText === 'function') {
      return emptyText();
    }
    return emptyText;
  }, [hasData, emptyText]);

  // Body
  var bodyTable = /*#__PURE__*/React.createElement(Body, {
    data: mergedData,
    measureColumnWidth: fixHeader || horizonScroll || isSticky
  });
  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {
    colWidths: flattenColumns.map(function (_ref5) {
      var width = _ref5.width;
      return width;
    }),
    columns: flattenColumns
  });
  var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/React.createElement("caption", {
    className: "".concat(prefixCls, "-caption")
  }, caption) : undefined;
  var dataProps = pickAttrs(props, {
    data: true
  });
  var ariaProps = pickAttrs(props, {
    aria: true
  });
  if (fixHeader || isSticky) {
    // >>>>>> Fixed Header
    var bodyContent;
    if (typeof customizeScrollBody === 'function') {
      bodyContent = customizeScrollBody(mergedData, {
        scrollbarSize: scrollbarSize,
        ref: scrollBodyRef,
        onScroll: onInternalScroll
      });
      headerProps.colWidths = flattenColumns.map(function (_ref6, index) {
        var width = _ref6.width;
        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;
        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {
          return colWidth;
        }
        if (process.env.NODE_ENV !== 'production') {
          warning(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');
        }
        return 0;
      });
    } else {
      bodyContent = /*#__PURE__*/React.createElement("div", {
        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),
        onScroll: onBodyScroll,
        ref: scrollBodyRef,
        className: classNames("".concat(prefixCls, "-body"))
      }, /*#__PURE__*/React.createElement(TableComponent, _extends({
        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {
          tableLayout: mergedTableLayout
        })
      }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {
        stickyOffsets: stickyOffsets,
        flattenColumns: flattenColumns
      }, summaryNode)));
    }

    // Fixed holder share the props
    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({
      noData: !mergedData.length,
      maxContentScroll: horizonScroll && mergedScrollX === 'max-content'
    }, headerProps), columnContext), {}, {
      direction: direction,
      stickyClassName: stickyClassName,
      onScroll: onInternalScroll
    });
    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {
      stickyTopOffset: offsetHeader,
      className: "".concat(prefixCls, "-header"),
      ref: scrollHeaderRef
    }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {
      stickyBottomOffset: offsetSummary,
      className: "".concat(prefixCls, "-summary"),
      ref: scrollSummaryRef
    }), renderFixedFooterTable), isSticky && scrollBodyRef.current && scrollBodyRef.current instanceof Element && /*#__PURE__*/React.createElement(StickyScrollBar, {
      ref: stickyRef,
      offsetScroll: offsetScroll,
      scrollBodyRef: scrollBodyRef,
      onScroll: onInternalScroll,
      container: container,
      direction: direction
    }));
  } else {
    // >>>>>> Unique table
    groupTableNode = /*#__PURE__*/React.createElement("div", {
      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),
      className: classNames("".concat(prefixCls, "-content")),
      onScroll: onInternalScroll,
      ref: scrollBodyRef
    }, /*#__PURE__*/React.createElement(TableComponent, _extends({
      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {
        tableLayout: mergedTableLayout
      })
    }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {
      stickyOffsets: stickyOffsets,
      flattenColumns: flattenColumns
    }, summaryNode)));
  }
  var fullTable = /*#__PURE__*/React.createElement("div", _extends({
    className: classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(prefixCls, "-rtl"), direction === 'rtl'), "".concat(prefixCls, "-ping-left"), pingedLeft), "".concat(prefixCls, "-ping-right"), pingedRight), "".concat(prefixCls, "-layout-fixed"), tableLayout === 'fixed'), "".concat(prefixCls, "-fixed-header"), fixHeader), "".concat(prefixCls, "-fixed-column"), fixColumn), "".concat(prefixCls, "-fixed-column-gapped"), fixColumn && hasGapFixed), "".concat(prefixCls, "-scroll-horizontal"), horizonScroll), "".concat(prefixCls, "-has-fix-left"), flattenColumns[0] && flattenColumns[0].fixed), "".concat(prefixCls, "-has-fix-right"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right')),
    style: style,
    id: id,
    ref: fullTableRef
  }, dataProps), title && /*#__PURE__*/React.createElement(Panel, {
    className: "".concat(prefixCls, "-title")
  }, title(mergedData)), /*#__PURE__*/React.createElement("div", {
    ref: scrollBodyContainerRef,
    className: "".concat(prefixCls, "-container")
  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {
    className: "".concat(prefixCls, "-footer")
  }, footer(mergedData)));
  if (horizonScroll) {
    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {
      onResize: onFullTableResize
    }, fullTable);
  }
  var fixedInfoList = useFixedInfo(flattenColumns, stickyOffsets, direction);
  var TableContextValue = React.useMemo(function () {
    return {
      // Scroll
      scrollX: mergedScrollX,
      // Table
      prefixCls: prefixCls,
      getComponent: getComponent,
      scrollbarSize: scrollbarSize,
      direction: direction,
      fixedInfoList: fixedInfoList,
      isSticky: isSticky,
      supportSticky: supportSticky,
      componentWidth: componentWidth,
      fixHeader: fixHeader,
      fixColumn: fixColumn,
      horizonScroll: horizonScroll,
      // Body
      tableLayout: mergedTableLayout,
      rowClassName: rowClassName,
      expandedRowClassName: expandableConfig.expandedRowClassName,
      expandIcon: mergedExpandIcon,
      expandableType: expandableType,
      expandRowByClick: expandableConfig.expandRowByClick,
      expandedRowRender: expandableConfig.expandedRowRender,
      expandedRowOffset: expandableConfig.expandedRowOffset,
      onTriggerExpand: onTriggerExpand,
      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,
      indentSize: expandableConfig.indentSize,
      allColumnsFixedLeft: flattenColumns.every(function (col) {
        return col.fixed === 'left';
      }),
      emptyNode: emptyNode,
      // Column
      columns: columns,
      flattenColumns: flattenColumns,
      onColumnResize: onColumnResize,
      colWidths: colWidths,
      // Row
      hoverStartRow: startRow,
      hoverEndRow: endRow,
      onHover: onHover,
      rowExpandable: expandableConfig.rowExpandable,
      onRow: onRow,
      getRowKey: getRowKey,
      expandedKeys: mergedExpandedKeys,
      childrenColumnName: mergedChildrenColumnName,
      rowHoverable: rowHoverable
    };
  }, [
  // Scroll
  mergedScrollX,
  // Table
  prefixCls, getComponent, scrollbarSize, direction, fixedInfoList, isSticky, supportSticky, componentWidth, fixHeader, fixColumn, horizonScroll,
  // Body
  mergedTableLayout, rowClassName, expandableConfig.expandedRowClassName, mergedExpandIcon, expandableType, expandableConfig.expandRowByClick, expandableConfig.expandedRowRender, expandableConfig.expandedRowOffset, onTriggerExpand, expandableConfig.expandIconColumnIndex, expandableConfig.indentSize, emptyNode,
  // Column
  columns, flattenColumns, onColumnResize, colWidths,
  // Row
  startRow, endRow, onHover, expandableConfig.rowExpandable, onRow, getRowKey, mergedExpandedKeys, mergedChildrenColumnName, rowHoverable]);
  return /*#__PURE__*/React.createElement(TableContext.Provider, {
    value: TableContextValue
  }, fullTable);
}
var RefTable = /*#__PURE__*/React.forwardRef(Table);
if (process.env.NODE_ENV !== 'production') {
  RefTable.displayName = 'Table';
}
export function genTable(shouldTriggerRender) {
  return makeImmutable(RefTable, shouldTriggerRender);
}
var ImmutableTable = genTable();
ImmutableTable.EXPAND_COLUMN = EXPAND_COLUMN;
ImmutableTable.INTERNAL_HOOKS = INTERNAL_HOOKS;
ImmutableTable.Column = Column;
ImmutableTable.ColumnGroup = ColumnGroup;
ImmutableTable.Summary = FooterComponents;
export default ImmutableTable;