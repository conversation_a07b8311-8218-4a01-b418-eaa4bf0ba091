import _extends from "@babel/runtime/helpers/esm/extends";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import classNames from 'classnames';
import { useEvent, warning } from 'rc-util';
import * as React from 'react';
import { INTERNAL_HOOKS } from "../constant";
import { makeImmutable } from "../context/TableContext";
import Table, { DEFAULT_PREFIX } from "../Table";
import Grid from "./BodyGrid";
import { StaticContext } from "./context";
import getValue from "rc-util/es/utils/get";
var renderBody = function renderBody(rawData, props) {
  var ref = props.ref,
    onScroll = props.onScroll;
  return /*#__PURE__*/React.createElement(Grid, {
    ref: ref,
    data: rawData,
    onScroll: onScroll
  });
};
function VirtualTable(props, ref) {
  var data = props.data,
    columns = props.columns,
    scroll = props.scroll,
    sticky = props.sticky,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? DEFAULT_PREFIX : _props$prefixCls,
    className = props.className,
    listItemHeight = props.listItemHeight,
    components = props.components,
    onScroll = props.onScroll;
  var _ref = scroll || {},
    scrollX = _ref.x,
    scrollY = _ref.y;

  // Fill scrollX
  if (typeof scrollX !== 'number') {
    if (process.env.NODE_ENV !== 'production') {
      warning(!scrollX, '`scroll.x` in virtual table must be number.');
    }
    scrollX = 1;
  }

  // Fill scrollY
  if (typeof scrollY !== 'number') {
    scrollY = 500;
    if (process.env.NODE_ENV !== 'production') {
      warning(false, '`scroll.y` in virtual table must be number.');
    }
  }
  var getComponent = useEvent(function (path, defaultComponent) {
    return getValue(components, path) || defaultComponent;
  });

  // Memo this
  var onInternalScroll = useEvent(onScroll);

  // ========================= Context ==========================
  var context = React.useMemo(function () {
    return {
      sticky: sticky,
      scrollY: scrollY,
      listItemHeight: listItemHeight,
      getComponent: getComponent,
      onScroll: onInternalScroll
    };
  }, [sticky, scrollY, listItemHeight, getComponent, onInternalScroll]);

  // ========================== Render ==========================
  return /*#__PURE__*/React.createElement(StaticContext.Provider, {
    value: context
  }, /*#__PURE__*/React.createElement(Table, _extends({}, props, {
    className: classNames(className, "".concat(prefixCls, "-virtual")),
    scroll: _objectSpread(_objectSpread({}, scroll), {}, {
      x: scrollX
    }),
    components: _objectSpread(_objectSpread({}, components), {}, {
      // fix https://github.com/ant-design/ant-design/issues/48991
      body: data !== null && data !== void 0 && data.length ? renderBody : undefined
    }),
    columns: columns,
    internalHooks: INTERNAL_HOOKS,
    tailor: true,
    ref: ref
  })));
}
var RefVirtualTable = /*#__PURE__*/React.forwardRef(VirtualTable);
if (process.env.NODE_ENV !== 'production') {
  RefVirtualTable.displayName = 'VirtualTable';
}
export function genVirtualTable(shouldTriggerRender) {
  return makeImmutable(RefVirtualTable, shouldTriggerRender);
}
export default genVirtualTable();