import _extends from "@babel/runtime/helpers/esm/extends";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["data", "index", "className", "rowKey", "style", "extra", "getHeight"];
import { useContext } from '@rc-component/context';
import classNames from 'classnames';
import * as React from 'react';
import Cell from "../Cell";
import TableContext, { responseImmutable } from "../context/TableContext";
import useRowInfo from "../hooks/useRowInfo";
import VirtualCell from "./VirtualCell";
import { StaticContext } from "./context";
import { computedExpandedClassName } from "../utils/expandUtil";
var BodyLine = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var data = props.data,
    index = props.index,
    className = props.className,
    rowKey = props.rowKey,
    style = props.style,
    extra = props.extra,
    getHeight = props.getHeight,
    restProps = _objectWithoutProperties(props, _excluded);
  var record = data.record,
    indent = data.indent,
    renderIndex = data.index;
  var _useContext = useContext(TableContext, ['prefixCls', 'flattenColumns', 'fixColumn', 'componentWidth', 'scrollX']),
    scrollX = _useContext.scrollX,
    flattenColumns = _useContext.flattenColumns,
    prefixCls = _useContext.prefixCls,
    fixColumn = _useContext.fixColumn,
    componentWidth = _useContext.componentWidth;
  var _useContext2 = useContext(StaticContext, ['getComponent']),
    getComponent = _useContext2.getComponent;
  var rowInfo = useRowInfo(record, rowKey, index, indent);
  var RowComponent = getComponent(['body', 'row'], 'div');
  var cellComponent = getComponent(['body', 'cell'], 'div');

  // ========================== Expand ==========================
  var rowSupportExpand = rowInfo.rowSupportExpand,
    expanded = rowInfo.expanded,
    rowProps = rowInfo.rowProps,
    expandedRowRender = rowInfo.expandedRowRender,
    expandedRowClassName = rowInfo.expandedRowClassName;
  var expandRowNode;
  if (rowSupportExpand && expanded) {
    var expandContent = expandedRowRender(record, index, indent + 1, expanded);
    var expandedClsName = computedExpandedClassName(expandedRowClassName, record, index, indent);
    var additionalProps = {};
    if (fixColumn) {
      additionalProps = {
        style: _defineProperty({}, '--virtual-width', "".concat(componentWidth, "px"))
      };
    }
    var rowCellCls = "".concat(prefixCls, "-expanded-row-cell");
    expandRowNode = /*#__PURE__*/React.createElement(RowComponent, {
      className: classNames("".concat(prefixCls, "-expanded-row"), "".concat(prefixCls, "-expanded-row-level-").concat(indent + 1), expandedClsName)
    }, /*#__PURE__*/React.createElement(Cell, {
      component: cellComponent,
      prefixCls: prefixCls,
      className: classNames(rowCellCls, _defineProperty({}, "".concat(rowCellCls, "-fixed"), fixColumn)),
      additionalProps: additionalProps
    }, expandContent));
  }

  // ========================== Render ==========================
  var rowStyle = _objectSpread(_objectSpread({}, style), {}, {
    width: scrollX
  });
  if (extra) {
    rowStyle.position = 'absolute';
    rowStyle.pointerEvents = 'none';
  }
  var rowNode = /*#__PURE__*/React.createElement(RowComponent, _extends({}, rowProps, restProps, {
    "data-row-key": rowKey,
    ref: rowSupportExpand ? null : ref,
    className: classNames(className, "".concat(prefixCls, "-row"), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, _defineProperty({}, "".concat(prefixCls, "-row-extra"), extra)),
    style: _objectSpread(_objectSpread({}, rowStyle), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)
  }), flattenColumns.map(function (column, colIndex) {
    return /*#__PURE__*/React.createElement(VirtualCell, {
      key: colIndex,
      component: cellComponent,
      rowInfo: rowInfo,
      column: column,
      colIndex: colIndex,
      indent: indent,
      index: index,
      renderIndex: renderIndex,
      record: record,
      inverse: extra,
      getHeight: getHeight
    });
  }));
  if (rowSupportExpand) {
    return /*#__PURE__*/React.createElement("div", {
      ref: ref
    }, rowNode, expandRowNode);
  }
  return rowNode;
});
var ResponseBodyLine = responseImmutable(BodyLine);
if (process.env.NODE_ENV !== 'production') {
  ResponseBodyLine.displayName = 'BodyLine';
}
export default ResponseBodyLine;