"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.genVirtualTable = genVirtualTable;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _classnames = _interopRequireDefault(require("classnames"));
var _rcUtil = require("rc-util");
var React = _interopRequireWildcard(require("react"));
var _constant = require("../constant");
var _TableContext = require("../context/TableContext");
var _Table = _interopRequireWildcard(require("../Table"));
var _BodyGrid = _interopRequireDefault(require("./BodyGrid"));
var _context = require("./context");
var _get = _interopRequireDefault(require("rc-util/lib/utils/get"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var renderBody = function renderBody(rawData, props) {
  var ref = props.ref,
    onScroll = props.onScroll;
  return /*#__PURE__*/React.createElement(_BodyGrid.default, {
    ref: ref,
    data: rawData,
    onScroll: onScroll
  });
};
function VirtualTable(props, ref) {
  var data = props.data,
    columns = props.columns,
    scroll = props.scroll,
    sticky = props.sticky,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? _Table.DEFAULT_PREFIX : _props$prefixCls,
    className = props.className,
    listItemHeight = props.listItemHeight,
    components = props.components,
    onScroll = props.onScroll;
  var _ref = scroll || {},
    scrollX = _ref.x,
    scrollY = _ref.y;

  // Fill scrollX
  if (typeof scrollX !== 'number') {
    if (process.env.NODE_ENV !== 'production') {
      (0, _rcUtil.warning)(!scrollX, '`scroll.x` in virtual table must be number.');
    }
    scrollX = 1;
  }

  // Fill scrollY
  if (typeof scrollY !== 'number') {
    scrollY = 500;
    if (process.env.NODE_ENV !== 'production') {
      (0, _rcUtil.warning)(false, '`scroll.y` in virtual table must be number.');
    }
  }
  var getComponent = (0, _rcUtil.useEvent)(function (path, defaultComponent) {
    return (0, _get.default)(components, path) || defaultComponent;
  });

  // Memo this
  var onInternalScroll = (0, _rcUtil.useEvent)(onScroll);

  // ========================= Context ==========================
  var context = React.useMemo(function () {
    return {
      sticky: sticky,
      scrollY: scrollY,
      listItemHeight: listItemHeight,
      getComponent: getComponent,
      onScroll: onInternalScroll
    };
  }, [sticky, scrollY, listItemHeight, getComponent, onInternalScroll]);

  // ========================== Render ==========================
  return /*#__PURE__*/React.createElement(_context.StaticContext.Provider, {
    value: context
  }, /*#__PURE__*/React.createElement(_Table.default, (0, _extends2.default)({}, props, {
    className: (0, _classnames.default)(className, "".concat(prefixCls, "-virtual")),
    scroll: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, scroll), {}, {
      x: scrollX
    }),
    components: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, components), {}, {
      // fix https://github.com/ant-design/ant-design/issues/48991
      body: data !== null && data !== void 0 && data.length ? renderBody : undefined
    }),
    columns: columns,
    internalHooks: _constant.INTERNAL_HOOKS,
    tailor: true,
    ref: ref
  })));
}
var RefVirtualTable = /*#__PURE__*/React.forwardRef(VirtualTable);
if (process.env.NODE_ENV !== 'production') {
  RefVirtualTable.displayName = 'VirtualTable';
}
function genVirtualTable(shouldTriggerRender) {
  return (0, _TableContext.makeImmutable)(RefVirtualTable, shouldTriggerRender);
}
var _default = exports.default = genVirtualTable();